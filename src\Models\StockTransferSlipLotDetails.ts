import { Column, <PERSON>tity, ManyToOne, PrimaryGeneratedColumn } from "typeorm";
import { StockTransferSlipDetails } from "./StockTransferSlipDetails";
import { Stock } from "./Stock";

@Entity()
export class StockTransferSlipLotDetails {
  @PrimaryGeneratedColumn()
  id!: number;

  @ManyToOne(() => StockTransferSlipDetails, detail => detail.lot_details)
  sts_detail!: StockTransferSlipDetails;

  @ManyToOne(() => Stock)
  stock!: Stock;

  @Column({ type: "numeric" })
  quantity_ordered!: number;

  @Column({ type: "numeric", default: 0 })
  quantity_sent!: number;
}